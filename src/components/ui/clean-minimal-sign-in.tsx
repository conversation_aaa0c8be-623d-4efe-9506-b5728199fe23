"use client";

import * as React from "react";
import { useState } from "react";
import { LogIn, Mail, Lock, Eye, EyeOff } from "lucide-react";
import { useRouter } from "@tanstack/react-router";
import { useAuth } from "@/hooks/use-auth";
import { useUserStore } from "@/stores/user-store";

/**
 * 简洁的登录组件
 * 支持钉钉登录和邮箱密码登录
 */
const SignIn = () => {
	const router = useRouter();
	const { login } = useAuth();
	const { setUser } = useUserStore();
	const [loginMethod, setLoginMethod] = useState<'dingtalk' | 'email'>('email');
	const [formData, setFormData] = useState({
		email: "",
		password: "",
	});
	const [showPassword, setShowPassword] = useState(false);
	const [isLoading, setIsLoading] = useState(false);
	const [error, setError] = useState("");
	/**
	 * Handle form input changes
	 */
	const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
		const { name, value } = e.target;
		setFormData(prev => ({
			...prev,
			[name]: value
		}));
		// Clear error when user starts typing
		if (error) setError("");
	};

	/**
	 * Handle email login
	 */
	const handleEmailLogin = async (e: React.FormEvent) => {
		e.preventDefault();

		if (!formData.email || !formData.password) {
			setError("请填写邮箱和密码");
			return;
		}

		setIsLoading(true);
		setError("");

		try {
			const response = await fetch("/api/auth/login", {
				method: "POST",
				headers: {
					"Content-Type": "application/json",
				},
				body: JSON.stringify(formData),
			});

			const data = await response.json();

			if (response.ok) {
				// Login successful
				login(data.user, data.token);
				// 同时更新用户store
				setUser(data.user);
				router.navigate({ to: "/" });
			} else {
				setError(data.message || "登录失败");
			}
		} catch (error) {
			console.error("Login error:", error);
			setError("网络错误，请稍后重试");
		} finally {
			setIsLoading(false);
		}
	};

	/**
	 * 处理钉钉登录
	 */
	const handleDingTalkLogin = () => {
		// 钉钉应用配置
		const clientId = import.meta.env.VITE_DINGTALK_CLIENT_ID || "your_client_id";
		const redirectUri = encodeURIComponent(
			window.location.origin + "/auth/dingtalk/callback"
		);
		const state = "dingtalk_login";

		if (!clientId || clientId === "your_client_id") {
			alert("钉钉应用配置不完整，请检查环境变量 VITE_DINGTALK_CLIENT_ID");
			return;
		}

		// 构建钉钉OAuth2.0授权URL
		const authUrl = `https://login.dingtalk.com/oauth2/auth?response_type=code&client_id=${clientId}&redirect_uri=${redirectUri}&state=${state}&scope=openid%20corpid&prompt=consent`;

		// 跳转到钉钉授权页面
		window.location.href = authUrl;
	};

	return (
		<div className="min-h-screen w-full flex items-center justify-center bg-white rounded-xl z-1">
			<div className="w-full max-w-md bg-gradient-to-b from-sky-50/50 to-white rounded-3xl shadow-xl shadow-opacity-10 p-8 flex flex-col items-center border border-blue-100 text-black">
				{/* 登录图标 */}
				<div className="flex items-center justify-center w-14 h-14 rounded-2xl bg-white mb-6 shadow-lg shadow-opacity-5">
					<LogIn className="w-7 h-7 text-black" />
				</div>

				{/* 标题和描述 */}
				<h2 className="text-2xl font-semibold mb-2 text-center">用户登录</h2>
				<p className="text-gray-500 text-sm mb-6 text-center">
					选择您的登录方式
				</p>

				{/* 登录方式切换 */}
				<div className="w-full flex mb-6 bg-gray-100 rounded-xl p-1">
					<button
						onClick={() => setLoginMethod('email')}
						className={`flex-1 py-2 px-4 rounded-lg text-sm font-medium transition ${
							loginMethod === 'email'
								? 'bg-white text-black shadow-sm'
								: 'text-gray-600 hover:text-black'
						}`}
					>
						邮箱登录
					</button>
					<button
						onClick={() => setLoginMethod('dingtalk')}
						className={`flex-1 py-2 px-4 rounded-lg text-sm font-medium transition relative ${
							loginMethod === 'dingtalk'
								? 'bg-white text-black shadow-sm'
								: 'text-gray-600 hover:text-black'
						}`}
					>
						钉钉登录
						<span className="absolute top-1 right-1 px-1.5 py-0.5 bg-red-500 text-white text-xxs rounded-full" style={{ fontSize: '0.6rem' }}>
							仅限中化兴海内部成员
						</span>
					</button>
				</div>

				{/* Error message */}
				{error && (
					<div className="w-full mb-4 p-3 bg-red-50 border border-red-200 rounded-lg text-red-600 text-sm">
						{error}
					</div>
				)}

				{/* 邮箱登录表单 */}
				{loginMethod === 'email' && (
					<form onSubmit={handleEmailLogin} className="w-full space-y-4">
						{/* Email field */}
						<div className="relative">
							<Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
							<input
								type="email"
								name="email"
								placeholder="邮箱地址"
								value={formData.email}
								onChange={handleInputChange}
								required
								className="w-full pl-10 pr-4 py-3 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
							/>
						</div>

						{/* Password field */}
						<div className="relative">
							<Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
							<input
								type={showPassword ? "text" : "password"}
								name="password"
								placeholder="密码"
								value={formData.password}
								onChange={handleInputChange}
								required
								className="w-full pl-10 pr-12 py-3 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
							/>
							<button
								type="button"
								onClick={() => setShowPassword(!showPassword)}
								className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
							>
								{showPassword ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
							</button>
						</div>

						{/* Submit button */}
						<button
							type="submit"
							disabled={isLoading}
							className="w-full bg-gradient-to-b from-blue-500 to-blue-600 text-white font-medium py-3 rounded-xl shadow hover:brightness-105 cursor-pointer transition disabled:opacity-50 disabled:cursor-not-allowed"
						>
							{isLoading ? "登录中..." : "登录"}
						</button>
					</form>
				)}

				{/* 钉钉登录 */}
				{loginMethod === 'dingtalk' && (
					<button
						onClick={handleDingTalkLogin}
						className="w-full bg-gradient-to-b from-blue-500 to-blue-600 text-white font-medium py-3 rounded-xl shadow hover:brightness-105 cursor-pointer transition mb-6 flex items-center justify-center gap-2"
					>
						<svg className="w-5 h-5" viewBox="0 0 24 24" fill="currentColor">
							<path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-1 17.93c-3.94-.49-7-3.85-7-7.93 0-.62.08-1.21.21-1.79L9 15v1c0 1.1.9 2 2 2v1.93zm6.9-2.54c-.26-.81-1-1.39-1.9-1.39h-1v-3c0-.55-.45-1-1-1H8v-2h2c.55 0 1-.45 1-1V7h2c1.1 0 2-.9 2-2v-.41c2.93 1.19 5 4.06 5 7.41 0 2.08-.8 3.97-2.1 5.39z" />
						</svg>
						使用钉钉登录
					</button>
				)}

				{/* 注册链接 */}
				{loginMethod === 'email' && (
					<div className="mt-6 text-center">
						<p className="text-gray-500 text-sm">
							还没有账户？{" "}
							<button
								onClick={() => router.navigate({ to: "/auth/register" })}
								className="text-blue-500 hover:text-blue-600 font-medium"
							>
								立即注册
							</button>
						</p>
					</div>
				)}

				{/* 帮助信息 */}
				<div className="text-center text-sm text-gray-500 mt-4">
					登录即表示您同意我们的{" "}
					<a
						href="/terms"
						className="text-blue-600 hover:underline font-medium"
					>
						服务条款
					</a>{" "}
					和{" "}
					<a
						href="/privacy"
						className="text-blue-600 hover:underline font-medium"
					>
						隐私政策
					</a>
				</div>
			</div>
		</div>
	);
};

export { SignIn };
